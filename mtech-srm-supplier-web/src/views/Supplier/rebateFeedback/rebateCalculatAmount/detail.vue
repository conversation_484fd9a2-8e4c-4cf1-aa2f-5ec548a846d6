<template>
  <div class="full-height">
    <!-- 头部表单 -->
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-btns">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input v-model="dataForm.rebateCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input v-model="dataForm.rebateName" type="text" maxlength="50" disabled />
            </mt-form-item>
            <mt-form-item prop="agreementTemplateCode" :label="$t('协议书模板')" label-style="top">
              <mt-select
                v-model="dataForm.agreementTemplateCode"
                :data-source="agreementTemplateList"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'text', value: 'agreementCode' }"
                disabled
                :placeholder="$t('请选择协议书模板')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <mt-input v-model="dataForm.company" disabled />
            </mt-form-item>
            <mt-form-item prop="agreementTemplateCode" :label="$t('协议书模板')" label-style="top">
              <mt-select
                v-model="dataForm.agreementTemplateCode"
                :data-source="agreementTemplateList"
                :fields="{ text: 'text', value: 'agreementCode' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="factory" :label="$t('工厂')" label-style="top">
              <mt-input v-model="dataForm.factory" disabled />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <mt-input v-model="dataForm.supplier" disabled />
            </mt-form-item>

            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-picker v-model="dataForm.startDate" disabled />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-picker v-model="dataForm.endDate" disabled />
            </mt-form-item>

            <mt-form-item prop="currency" :label="$t('币种')" label-style="top">
              <mt-select
                v-model="dataForm.currency"
                :data-source="currencyList"
                disabled
                :fields="{ text: 'text', value: 'currencyCode' }"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select v-model="dataForm.status" :data-source="statusList" disabled />
            </mt-form-item>

            <mt-form-item prop="supplierHandleRemark" :label="$t('供方处理意见')" label-style="top">
              <mt-select v-model="dataForm.supplierHandleRemark" :data-source="handleRemarkList" />
            </mt-form-item>

            <mt-form-item
              v-if="showKtField"
              prop="taxNumber"
              :label="$t('购买方税号')"
              label-style="top"
            >
              <mt-input v-model="dataForm.taxNumber" disabled />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="writeOffAmount"
              :label="$t('冲销金额')"
              label-style="top"
            >
              <mt-input v-model="dataForm.writeOffAmount" disabled type="number" min="0" />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="writeOffDesc"
              :label="$t('冲销说明')"
              label-style="top"
            >
              <mt-input v-model="dataForm.writeOffDesc" disabled />
            </mt-form-item>

            <mt-form-item
              v-if="showKtField"
              prop="redConfirmVoucherNumber"
              :label="$t('红字确认单编号')"
              label-style="top"
            >
              <mt-input
                v-model="dataForm.redConfirmVoucherNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>

            <mt-form-item
              v-show="$route.query.tabIndex == 1"
              prop="printStartDate"
              :label="$t('打印起始日')"
              label-style="top"
            >
              <mt-date-picker
                v-model="dataForm.printStartDate"
                clearable
                :allow-edit="false"
                :open-on-focus="true"
                format="yyyy-MM-dd"
                :render-day-cell="(args) => handleRenderDayCell('start', args)"
                :placeholder="$t('请选择打印起始日')"
              />
            </mt-form-item>
            <mt-form-item
              v-show="$route.query.tabIndex == 1"
              prop="printEndDate"
              :label="$t('打印结束日')"
              label-style="top"
            >
              <mt-date-picker
                v-model="dataForm.printEndDate"
                clearable
                :allow-edit="false"
                :open-on-focus="true"
                format="yyyy-MM-dd"
                :render-day-cell="(args) => handleRenderDayCell('end', args)"
                :placeholder="$t('请选择打印结束日')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <!-- 内容部分table -->
    <div class="body-container">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab" />
      <div v-if="currentTabIndex === 0" class="table-item">
        <detail-table
          ref="detailRef"
          :detail-info="dataForm"
          :list="detailList"
          :show-kt-field="showKtField"
        />
      </div>
      <!-- 附件列表 -->
      <div v-if="currentTabIndex === 1" class="attachment-container">
        <!-- 采方附件 -->
        <div class="attachment-section">
          <div class="section-header">
            <div class="section-title">
              <div class="title-prefix" />
              <span class="title-text">{{ $t('采方附件') }}</span>
              <span class="attachment-count" v-if="purAttachmentList.length > 0">
                ({{ purAttachmentList.length }})
              </span>
            </div>
            <div
              class="section-toggle"
              :class="{ 'is-collapsed': !purIsExpand }"
              @click="purIsExpand = !purIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <div class="section-content" v-show="purIsExpand">
            <purchase-attachment
              ref="purAttachmentRef"
              :detail-info="dataForm"
              :list="purAttachmentList"
            />
          </div>
        </div>

        <!-- 供方附件 -->
        <div class="attachment-section">
          <div class="section-header">
            <div class="section-title">
              <div class="title-prefix" />
              <span class="title-text">{{ $t('供方附件') }}</span>
              <span class="attachment-count" v-if="supAttachmentList.length > 0">
                ({{ supAttachmentList.length }})
              </span>
            </div>
            <div
              class="section-toggle"
              :class="{ 'is-collapsed': !supIsExpand }"
              @click="supIsExpand = !supIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <div class="section-content" v-show="supIsExpand">
            <supplier-attachment
              ref="supAttachmentRef"
              :detail-info="dataForm"
              :list="supAttachmentList"
            />
          </div>
        </div>

        <!-- 冲销附件 -->
        <div v-if="showKtField" class="attachment-section">
          <div class="section-header">
            <div class="section-title">
              <div class="title-prefix" />
              <span class="title-text">{{ $t('冲销附件') }}</span>
              <span class="attachment-count" v-if="writeOffAttachmentList.length > 0">
                ({{ writeOffAttachmentList.length }})
              </span>
            </div>
            <div
              class="section-toggle"
              :class="{ 'is-collapsed': !writeOffIsExpand }"
              @click="writeOffIsExpand = !writeOffIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <div class="section-content" v-show="writeOffIsExpand">
            <write-off-attachment
              ref="writeOffAttachmentRef"
              :detail-info="dataForm"
              :list="writeOffAttachmentList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, handleRemarkList } from './config/index'
import DetailTable from './components/detailTable.vue'
import PurchaseAttachment from './components/purchaseAttachment.vue'
import SupplierAttachment from './components/supplierAttachment.vue'
import WriteOffAttachment from './components/writeOffAttachment.vue'

export default {
  components: { DetailTable, PurchaseAttachment, SupplierAttachment, WriteOffAttachment },
  data() {
    return {
      type: 'detail',
      isExpand: true,
      dataForm: {
        factoryCodeList: [],
        status: 1
      },
      showKtField: false, // 是否显示KT事业部字段
      formRules: {
        supplierHandleRemark: [
          { required: true, message: this.$t('请选择供方处理意见'), trigger: 'blur' }
        ],
        redConfirmVoucherNumber: [
          { required: true, message: this.$t('请输入红字确认单编号'), trigger: 'blur' }
        ]
      },
      statusList,
      handleRemarkList,
      agreementTemplateList: [],
      currencyList: [],
      purIsExpand: true,
      supIsExpand: true,
      writeOffIsExpand: true,
      detailList: [], //明细列表
      purAttachmentList: [], //采方附件
      supAttachmentList: [], // 供方附件
      writeOffAttachmentList: [], // 冲销附件
      isInit: true,
      currentTabIndex: 0,
      tabList: [
        {
          title: this.$t('明细')
        },
        {
          title: this.$t('附件')
        }
      ],
      selectedDetailList: []
    }
  },
  computed: {
    editable() {
      return this.dataForm.status === 2
    },
    toolbar() {
      const toolbar = [
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: this.dataForm.status === 12
        },
        {
          code: 'print',
          name: this.$t('打印'),
          status: '',
          isHidden: this.$route.query.tabIndex != 1
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCurrencyList()
      this.getDetailInfo()
    },
    // 获取明细信息
    async getDetailInfo() {
      this.isInit = true
      const res = await this.$API.rebateManagement.getRebateAmountItemDetailById({
        id: this.$route.query.id,
        queryType: this.$route.query.tabIndex == 0 ? 1 : 2
      })
      if (res.code === 200) {
        const {
          siteInfo,
          startDate,
          companyCode,
          companyName,
          endDate,
          feedbackDate,
          itemResponseList,
          purchaseFileList,
          supplierFileList,
          writeOffFileList,
          supplierCode,
          supplierName
        } = res.data
        const tempfactoryList = []
        const factoryList = siteInfo ? JSON.parse(siteInfo) : []
        factoryList.forEach((item) => tempfactoryList.push(item.code + '-' + item.name))
        this.dataForm = {
          ...res.data,
          factory: tempfactoryList.join('; '),
          company: companyCode + '-' + companyName,
          supplier: supplierCode + '-' + supplierName,
          startDate: startDate ? dayjs(Number(startDate)).format('YYYY-MM-DD') : null,
          endDate: endDate ? dayjs(Number(endDate)).format('YYYY-MM-DD') : null,
          feedbackDate: feedbackDate ? dayjs(Number(feedbackDate)).format('YYYY-MM-DD') : null
        }
        this.detailList = itemResponseList || []
        this.purAttachmentList = purchaseFileList
        this.supAttachmentList = supplierFileList
        this.writeOffAttachmentList = writeOffFileList || []

        this.getAgreementTemplateList(res.data.companyCode)
        this.checkIsKtCompany(res.data.companyCode)
      }
    },
    // 协议模板下拉列表
    async getAgreementTemplateList(companyCode) {
      const params = {
        status: 1,
        companyCode
      }
      const res = await this.$API.rebateFeedback.queryAgreemenTempalteList(params)
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.agreementCode + '-' + item.agreementName
        })
        this.agreementTemplateList = res.data || []
      }
    },
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    },
    // 检查是否为KT事业部
    async checkIsKtCompany(companyCode) {
      if (!companyCode) return
      try {
        const res = await this.$API.rebateFeedback.isKtCompany({ companyCode })
        if (res.code === 200) {
          this.showKtField = res.data === true
        }
      } catch (error) {
        console.warn('检查KT事业部失败:', error)
        this.showKtField = false
      }
    },
    // 限制日期选择器选择
    handleRenderDayCell(type, args) {
      if (type === 'start' && this.dataForm.printEndDate) {
        args.isDisabled = args.date.valueOf() >= this.dataForm.printEndDate.valueOf() + 86400000
        return
      }
      if (type === 'end' && this.dataForm.printStartDate) {
        args.isDisabled = args.date.valueOf() <= this.dataForm.printStartDate.valueOf() - 86400000
        return
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        case 'save':
          this.handleSave()
          break
        case 'print':
          this.handlePrint()
          break
        default:
          break
      }
    },
    // 保存
    handleSave() {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          // 根据保存接口.md优化传参结构
          const params = {
            agreementCode: this.dataForm.rebateCode, // 协议编码
            status: this.dataForm.status || 0, // 状态（数字类型）
            redConfirmVoucherNumber: this.dataForm.redConfirmVoucherNumber || '', // 红字确认单编号
            purchaseFileList: this.purAttachmentList || [], // 采方附件
            supplierFileList:
              this.currentTabIndex === 1
                ? this.$refs.supAttachmentRef?.dataList || []
                : this.supAttachmentList || [] // 供方附件
          }
          const res = await this.$API.rebateManagement.rebateAmountSaveItem(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$router.replace({
              name: 'rebate-calculat-amount-sup'
            })
          }
        }
      })
    },
    // 打印
    async handlePrint() {
      const { rebateCode, printStartDate, printEndDate } = this.dataForm
      if (!printStartDate || !printEndDate) {
        this.$toast({
          type: 'warning',
          content: !printStartDate ? this.$t('请选择打印起始日！') : this.$t('请选择打印结束日！')
        })
        return
      }
      const params = {
        rebateCode,
        startDate: printStartDate.valueOf(),
        endDate: printEndDate.valueOf() + 86400000
      }
      let buffer = await this.$API.rebateFeedback.printRebateAmountConfirmDetail(params)
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    handleSelectTab(index) {
      index === 0 && (this.supAttachmentList = this.$refs.supAttachmentRef?.dataList)
      index === 1 && (this.selectedDetailList = this.$refs.detailRef?.tableRef.getCheckboxRecords())
      this.currentTabIndex = index
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  width: 100%;
  height: auto;
  padding: 8px;
  background: #fff;
  overflow: scroll;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-btns {
      text-align: right;
      margin-bottom: 10px;
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 8px 8px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 8px 8px 0 0;
    }
  }
}

.body-container {
  .table-item {
    width: 100%;
    margin-top: 10px;
    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;
        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
      }
      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}

// 新增附件容器样式
.attachment-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 10px;

  @media (max-width: 768px) {
    gap: 12px;
  }
}

.attachment-section {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d0d0d0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    user-select: none;

    &:hover {
      background: #f5f5f5;
    }

    .section-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #333;

      .title-prefix {
        width: 4px;
        height: 16px;
        background-color: #409eff;
        margin-right: 12px;
        border-radius: 2px;
      }

      .title-text {
        margin-right: 8px;
      }

      .attachment-count {
        color: #666;
        font-size: 12px;
        font-weight: normal;
      }
    }

    .section-toggle {
      color: #409eff;
      font-size: 16px;
      transition: transform 0.3s ease;
      transform: rotate(270deg);

      &.is-collapsed {
        transform: rotate(90deg);
      }

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .section-content {
    padding: 0;
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
  }

  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }
}
</style>
